/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * UI Router API
 * Defines the specification for the UI Catalog API
 * OpenAPI spec version: 1.0.2
 */
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import { useAxiosMutator } from '../lib/axiosMutator';
import type { ErrorType } from '../lib/axiosMutator';
import type { AccountResponse, AuthErrorResponse } from './models';

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

export const getAccount = (
  options?: SecondParameter<typeof useAxiosMutator>,
  signal?: AbortSignal,
) => {
  return useAxiosMutator<AccountResponse>(
    { url: `/ui/auth/account`, method: 'GET', signal },
    options,
  );
};

export const getGetAccountQueryKey = () => {
  return [`/ui/auth/account`] as const;
};

export const getGetAccountInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof getAccount>>>,
  TError = ErrorType<AuthErrorResponse>,
>(options?: {
  query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
  request?: SecondParameter<typeof useAxiosMutator>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAccountQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccount>>> = ({ signal }) =>
    getAccount(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof getAccount>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAccountInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof getAccount>>>;
export type GetAccountInfiniteQueryError = ErrorType<AuthErrorResponse>;

export function useGetAccountInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getAccount>>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAccount>>,
          TError,
          Awaited<ReturnType<typeof getAccount>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAccountInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getAccount>>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAccount>>,
          TError,
          Awaited<ReturnType<typeof getAccount>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAccountInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getAccount>>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

export function useGetAccountInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getAccount>>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetAccountInfiniteQueryOptions(options);

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getGetAccountQueryOptions = <
  TData = Awaited<ReturnType<typeof getAccount>>,
  TError = ErrorType<AuthErrorResponse>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
  request?: SecondParameter<typeof useAxiosMutator>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAccountQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccount>>> = ({ signal }) =>
    getAccount(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAccount>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAccountQueryResult = NonNullable<Awaited<ReturnType<typeof getAccount>>>;
export type GetAccountQueryError = ErrorType<AuthErrorResponse>;

export function useGetAccount<
  TData = Awaited<ReturnType<typeof getAccount>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAccount>>,
          TError,
          Awaited<ReturnType<typeof getAccount>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAccount<
  TData = Awaited<ReturnType<typeof getAccount>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAccount>>,
          TError,
          Awaited<ReturnType<typeof getAccount>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };
export function useGetAccount<
  TData = Awaited<ReturnType<typeof getAccount>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

export function useGetAccount<
  TData = Awaited<ReturnType<typeof getAccount>>,
  TError = ErrorType<AuthErrorResponse>,
>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAccount>>, TError, TData>>;
    request?: SecondParameter<typeof useAxiosMutator>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetAccountQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
