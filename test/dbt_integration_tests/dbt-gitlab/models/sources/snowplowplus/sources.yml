version: 2

sources:
  - name: fishtown_snowplow
    description: These are the source tables for Snowplow events that were hosted on Fishtown Analytics' infrastructure.
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: snowplow
    loader: Deprecated # informational only (free text)
    loaded_at_field: uploaded_at # configure for all sources

    quoting:
      database: false
      schema: false
      identifier: false

    tables:
      - name: events
        columns:
          - name: jsontext
          - name: uploaded_at

      - name: events_sample
        identifier: fishtown_events_sample
        description: This is a 1 to 7 day sample of the main event data used for development and CI validation. Previously, we had a filter based on the event date for development querying. This sample provides significant performance gains primarily by scanning a smaller table than the full primary table.
        columns:
          - name: uploaded_at
            data_tests:
              - not_null
        freshness: null

      - name: bad_events
        description: These are the events that weren't processed due to some error.
        columns:
          - name: jsontext

  - name: gitlab_snowplow
    description: These are the source tables for Snowplow events that are hosted on GitLab's infrastructure.
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: snowplow
    loader: Snowpipe
    loaded_at_field: uploaded_at # configure for all sources

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: events
        identifier: gitlab_events
        columns:
          - name: uploaded_at

      - name: events_sample
        identifier: gitlab_events_sample
        description: This is a 1 to 7 day sample of the main event data used for development and CI validation. Previously, we had a filter based on the event date for development querying. This sample provides significant performance gains primarily by scanning a smaller table than the full primary table.
        columns:
          - name: uploaded_at
            data_tests:
              - not_null
        freshness:
          warn_after: {count: 16, period: day}
          error_after: {count: 21, period: day}

      - name: bad_events
        description: These are the events that weren't processed due to some error.
        identifier: gitlab_bad_events
        columns:
          - name: jsontext
