version: 2

models:
  - name: summary_gcp_billing_source
    description: Base model for gcp billing summary data. Column descriptions can be found at https://cloud.google.com/billing/docs/how-to/export-data-bigquery-tables/standard-usage
    columns:
      - name: primary_key
        data_tests:
          - not_null
          - unique:
              config:
                where: "export_time >= DATEADD('day',-3,CURRENT_DATE())"
      - name: billing_account_id
        data_tests:
          - not_null
      - name: partition_date
      - name: uploaded_at
        description: derived from the `gcs_export_time` that is generated when the data is exported from BigQuery
      - name: occurrence_multiplier
        description: Given that the unique key is determined by the valued of all of the columns this field indicated how many times the row was repeated in the original data.  It is used to correctly calculated numeric values in this and any downstream columns.

  - name: detailed_gcp_billing_source
    description: Base model for gcp billing summary data. Column descriptions can be found at https://cloud.google.com/billing/docs/how-to/export-data-bigquery-tables/detailed-usage
    columns:
      - name: primary_key
        data_tests:
          - not_null
          - unique:
              config:
                where: "invoice_month >= DATEADD('month',-3,CURRENT_DATE())"
      - name: billing_account_id
        data_tests:
          - not_null
      - name: partition_date
      - name: uploaded_at
        description: derived from the `gcs_export_time` that is generated when the data is exported from BigQuery
      - name: occurrence_multiplier
        description: Given that the unique key is determined by the valued of all of the columns this field indicated how many times the row was repeated in the original data.  It is used to correctly calculated numeric values in this and any downstream columns.

