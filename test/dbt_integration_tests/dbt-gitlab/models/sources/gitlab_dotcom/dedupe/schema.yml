version: 2

models:
  - name: gitlab_dotcom_alert_management_alert_assignees_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_alert_management_alerts_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_alert_management_http_integrations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_analytics_cycle_analytics_group_stages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_application_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approval_merge_request_rule_sources_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approval_merge_request_rules_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approval_project_rules_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approval_project_rules_groups_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approval_project_rules_users_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_approvals_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_audit_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_audit_events_external_audit_event_destinations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_authentication_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_award_emoji_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_banned_users_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_batched_background_migrations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_board_assignees_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_board_labels_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_boards_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_bulk_imports_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_build_trace_chunks_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_builds_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_builds_metadata_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_builds_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_group_variables_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_job_artifacts_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_namespace_monthly_usages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_pipeline_artifacts_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_pipeline_schedule_variables_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_pipeline_schedules_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_pipelines_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_pipelines_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_runner_projects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_runners_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_secure_files_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_sources_pipelines_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_subscriptions_projects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_stages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_trigger_requests_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ci_triggers_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_cluster_agent_tokens_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_cluster_agents_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_cluster_groups_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_cluster_projects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_cert_managers_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_crossplane_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_elastic_stacks_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_helm_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_ingress_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_jupyter_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_knative_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_prometheus_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_applications_runners_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_clusters_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_container_expiration_policies_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_compliance_management_frameworks_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_csv_issue_imports_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_dast_profiles_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_deployment_merge_requests_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_deployment_approvals_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_deployments_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_elasticsearch_indexed_namespaces_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_environments_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_epic_issues_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_epics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_epics_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_external_status_checks_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_fork_network_members_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_grafana_integrations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_group_audit_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_identities_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_incident_management_issuable_escalation_statuses_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_incident_management_timeline_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_incident_management_timeline_event_tags_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_incident_management_timeline_event_tag_links_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_integrations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_issuable_severities_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_issue_metrics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_issues_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_jira_connect_subscriptions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_jira_imports_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_jira_tracker_data_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_keys_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_label_priorities_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_labels_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_labels_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ldap_group_links_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_lfs_objects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_lists_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_member_roles_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_merge_request_diffs_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_merge_request_metrics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_merge_requests_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_merge_requests_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_milestone_releases_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_milestones_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_milestones_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ml_candidates_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_ml_experiments_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespace_details_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespace_root_storage_statistics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespace_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespace_statistics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespaces_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_namespaces_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_notes_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_notes_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_notification_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_oauth_access_tokens_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_operations_feature_flags_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_packages_packages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_pages_domains_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_plans_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_pool_repositories_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_programming_languages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_auto_devops_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_compliance_framework_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_ci_cd_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_error_tracking_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_feature_usages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_features_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_incident_management_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_mirror_data_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_repositories_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_project_statistics_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_projects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_protected_branch_merge_access_levels_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_protected_branch_push_access_levels_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_protected_environment_approval_rules_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_protected_environments_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_related_epic_links_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_releases_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_remote_mirrors_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_repository_languages_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_requirements_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_resource_label_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_resource_milestone_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_resource_weight_events_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_routes_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_saml_group_links_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_saml_providers_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_security_orchestration_policy_configurations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_security_scans_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_sentry_issues_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_slack_integrations_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_snippets_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_status_page_published_incidents_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_status_page_settings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_subscriptions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_subscription_add_ons_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_subscription_add_on_purchases_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_subscription_user_add_on_assignments_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_subscription_user_add_on_assignment_versions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_suggestions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_system_note_metadata_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_terraform_states_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_todos_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_uploads_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_user_details_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_user_preferences_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_users_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_users_ops_dashboard_projects_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_vulnerabilities_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_vulnerability_identifiers_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_vulnerability_occurrences_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_vulnerability_state_transitions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_web_hooks_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_work_item_parent_links_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_work_item_types_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_workspaces_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_workspaces_agent_configs_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_zoom_meetings_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_container_repositories_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_merge_request_predictions_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_routes_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_projects_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_issues_internal_only_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_zoekt_enabled_namespaces_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
  - name: gitlab_dotcom_zoekt_indices_dedupe_source
    description: '{{ doc("gitlab_dotcom_dedupe_docs") }}'
