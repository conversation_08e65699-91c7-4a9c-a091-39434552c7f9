version: 2

sources:
  - name: rally_webhook_stitch
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: rally_webhook_stitch
    loader: Airflow
    loaded_at_field: _SDC_BATCHED_AT

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 24, period: hour}
      error_after: {count: 48, period: hour}

    tables:
      - name: data
