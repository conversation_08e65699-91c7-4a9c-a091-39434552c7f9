version: 2
 
models:
 - name: fabrication_stats_source
   description: Base model for fabrication_stats
   columns:
     - name: timestamp
       description: Time when the test was run.
     - name: tags_resource
       description: Type of resource created - MR, Issue, project, etc.
     - name: fabrication_method
       description: Resource was fabricated via UI or API.
     - name: tags_http_method
       description: The HTTP method used.
     - name: tags_run_type
       description: Name of the test run in CI.
     - name: is_merge_request
       description: Boolean value to idetify if this resource a merge_request?
     - name: info
       description: Any information regarding the resource created.
     - name: job_url
       description: Name of the CI Job where tests are run.
     - name: combined_composite_keys
       description: Combined Composite Keys for all columns
       data_tests:
         - unique
 - name: main_test_stats_e2e_source
   description: Base model for main_test_stats
   columns:
     - name: main_test_stats_id
     - name: tags_testcase
       description: Url of the testcase in the gitlab project.
     - name: tags_file_path
       description: Relative file path of the testfile in gitlab project.
     - name: tags_name
       description: Description of the testcase.
     - name: tags_product_group
       description: Prdocut group under which the testcase falls under.
     - name: tags_stage
       description: Stage under which the testcase falls under.
     - name: job_id
       description: job_id of the CI Job that triggered the test
     - name: tags_job_name
       description: Job name of the CI Job that triggered the test.
     - name: job_url
       description: Job URL of the CI Job that triggered the test.
     - name: pipeline_id
       description: Pipeline ID of the CI pipeline that triggered the test.
     - name: pipeline_url
       description: Pipeline URL of the CI pipeline that triggered the test.
     - name: is_merge_request
       description: Is this test triggered from a merge request?
     - name: is_smoke
       description: Is this test part of the smoke tests bucket?
     - name: is_quarantined
       description: Is this test currently quarantined?
     - name: tags_run_type
       description: The test job from which the test is triggered.
     - name: tags_status
       description: Has the test passed or failed?
     - name: ui_fabrication
       description: Number of milliseconds to finish fabrication of test resources via UI.
     - name: api_fabrication
       description: Number of milliseconds to finish fabrication of test resources via API.
     - name: total_fabrication
       description: Total number of milliseconds to finish fabrication of test resources via API and UI.0
     - name: combined_composite_keys
       description: Combined Composite Keys for all columns
       data_tests:
         - unique
 - name: test_stats_e2e_source
   description: Base model for all_test_stats and test_metrics
   columns:
     - name: test_stats_id
       description: Unique id to identify a testcase in RSpec.
     - name: tags_testcase
       description: Url of the testcase in the gitlab project.
     - name: tags_file_path
       description: Relative file path of the testfile in gitlab project.
     - name: name
       description: Description of the testcase.
     - name: tags_product_group
       description: Prdocut group under which the testcase falls under.
     - name: tags_stage
       description: Stage under which the testcase falls under.
     - name: job_id
       description: job_id of the CI Job that triggered the test
     - name: tags_job_name
       description: job_name of the CI Job that triggered the test.
     - name: job_url
       description: job_url of the CI Job that triggered the test.
     - name: pipeline_id
       description: pipeline_id of the CI pipeline that triggered the test.
     - name: pipeline_url
       description: pipeline_url of the CI pipeline that triggered the test.
     - name: is_merge_request
       description: Is this test triggered from a merge request?
     - name: is_smoke
       description: Is this test part of the smoke tests bucket?
     - name: is_quarantined
       description: Is this test currently quarantined?
     - name: tags_run_type
       description: The test job from which the test is triggered.
     - name: tags_status
       description: Has the test passed or failed?
     - name: ui_fabrication
       description: Number of milliseconds to finish fabrication of test resources via UI.
     - name: api_fabrication
       description: Number of milliseconds to finish fabrication of test resources via API.
     - name: total_fabrication
       description: Total number of milliseconds to finish fabrication of test resources via API and UI.0
     - name: combined_composite_keys
       description: Combined Composite Keys for all columns
       data_tests:
         - unique