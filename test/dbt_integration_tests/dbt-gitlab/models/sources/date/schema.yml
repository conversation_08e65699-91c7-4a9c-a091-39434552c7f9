version: 2

models:
    - name: date_details_source
      description: Join on this table to add additional date complexity
      columns:
          - name: date_day
            description: '{{ doc("date_date_day") }}'
            data_tests:
               - not_null
               - unique
          - name: date_actual
            description: '{{ doc("date_date_actual") }}'
            data_tests:
               - not_null
               - unique
          - name: day_name
            description: '{{ doc("date_day_name") }}'
            data_tests:
               - not_null
          - name: month_actual
            description: '{{ doc("date_month_actual") }}'
            data_tests:
               - not_null
          - name: year_actual
            description: '{{ doc("date_year_actual") }}'
            data_tests:
               - not_null
          - name: quarter_actual
            description: '{{ doc("date_quarter_actual") }}'
            data_tests:
               - not_null
          - name: day_of_week
            description: '{{ doc("date_day_of_week") }}'
            data_tests:
               - not_null
          - name: first_day_of_week
            description: '{{ doc("date_first_day_of_week") }}'
            data_tests:
               - not_null
          - name: week_of_year
            description: '{{ doc("date_week_of_year") }}'
            data_tests:
               - not_null
          - name: day_of_month
            description: '{{ doc("date_day_of_month") }}'
            data_tests:
               - not_null
          - name: day_of_quarter
            description: '{{ doc("date_day_of_quarter") }}'
            data_tests:
               - not_null
          - name: day_of_year
            description: '{{ doc("date_day_of_year") }}'
            data_tests:
               - not_null
          - name: fiscal_year
            description: '{{ doc("date_fiscal_year") }}'
            data_tests:
               - not_null
          - name: fiscal_quarter
            description: '{{ doc("date_fiscal_quarter") }}'
            data_tests:
               - not_null
          - name: day_of_fiscal_quarter
            description: '{{ doc("date_day_of_fiscal_quarter") }}'
            data_tests:
               - not_null
          - name: day_of_fiscal_year
            description: '{{ doc("date_day_of_fiscal_year") }}'
            data_tests:
               - not_null
          - name: month_name
            description: '{{ doc("date_month_name") }}'
            data_tests:
               - not_null
          - name: first_day_of_month
            description: '{{ doc("date_first_day_of_month") }}'
            data_tests:
               - not_null
          - name: last_day_of_month
            description: '{{ doc("date_last_day_of_month") }}'
            data_tests:
               - not_null
          - name: first_day_of_year
            description: '{{ doc("date_first_day_of_year") }}'
            data_tests:
               - not_null
          - name: last_day_of_year
            description: '{{ doc("date_last_day_of_year") }}'
            data_tests:
               - not_null
          - name: first_day_of_quarter
            description: '{{ doc("date_first_day_of_quarter") }}'
            data_tests:
               - not_null
          - name: last_day_of_quarter
            description: '{{ doc("date_last_day_of_quarter") }}'
            data_tests:
               - not_null
          - name: first_day_of_fiscal_quarter
            description: '{{ doc("date_first_day_of_fiscal_quarter") }}'
            data_tests:
               - not_null
          - name: last_day_of_fiscal_quarter
            description: '{{ doc("date_last_day_of_fiscal_quarter") }}'
            data_tests:
               - not_null
          - name: first_day_of_fiscal_year
            description: '{{ doc("date_first_day_of_fiscal_year") }}'
            data_tests:
               - not_null
          - name: last_day_of_fiscal_year
            description: '{{ doc("date_last_day_of_fiscal_year") }}'
            data_tests:
               - not_null
          - name: week_of_fiscal_year
            description: '{{ doc("date_week_of_fiscal_year") }}'
            data_tests:
               - not_null
          - name: month_of_fiscal_year
            description: '{{ doc("date_month_of_fiscal_year") }}'
            data_tests:
               - not_null
          - name: last_day_of_week
            description: '{{ doc("date_last_day_of_week") }}'
            data_tests:
               - not_null
          - name: quarter_name
            description: '{{ doc("date_quarter_name") }}'
            data_tests:
               - not_null
          - name: fiscal_quarter_name
            description: '{{ doc("date_fiscal_quarter_name") }}'
            data_tests:
               - not_null
          - name: fiscal_quarter_name_fy
            description: '{{ doc("date_fiscal_quarter_name_fy") }}'
            data_tests:
               - not_null
          - name: fiscal_quarter_number_absolute
            description: '{{ doc("date_fiscal_quarter_number_absolute") }}'
            data_tests:
              - not_null
          - name: fiscal_month_name
            description: '{{ doc("date_fiscal_month_name") }}'
            data_tests:
              - not_null
          - name: fiscal_month_name_fy
            description: '{{ doc("date_fiscal_month_name_fy") }}'
            data_tests:
              - not_null
          - name: holiday_desc
            description: '{{ doc("date_holiday_desc") }}'
          - name: is_holiday
            description: '{{ doc("date_is_holiday") }}'
            data_tests:
               - not_null
          - name: last_month_of_fiscal_quarter
            description: '{{ doc("date_last_month_of_fiscal_quarter") }}'
            data_tests:
              - not_null
          - name: is_first_day_of_last_month_of_fiscal_quarter
            description: '{{ doc("date_is_first_day_of_last_month_of_fiscal_quarter") }}'
          - name: last_month_of_fiscal_year
            description: '{{ doc("date_last_month_of_fiscal_year") }}'
            data_tests:
              - not_null
          - name: is_first_day_of_last_month_of_fiscal_year
            description: '{{ doc("date_is_first_day_of_last_month_of_fiscal_year") }}'
          - name: snapshot_date_fpa
            description: '{{ doc("date_snapshot_date_fpa") }}'
            data_tests:
              - not_null
          - name: snapshot_date_fpa_fifth
            description: '{{ doc("date_snapshot_date_fpa_fifth") }}'
            data_tests:
              - not_null
          - name: snapshot_date_billings
            description: '{{ doc("date_snapshot_date_billings") }}'
            data_tests:
              - not_null
          - name: days_in_month_count
            description: '{{ doc("date_days_in_month_count") }}' 
            data_tests:
              - not_null
          - name: is_third_business_day_of_fiscal_quarter
            description: '{{ doc("is_third_business_day_of_fiscal_quarter") }}'