version: 2

models:
    - name: instance_sql_errors
      tags: ["product", "service_ping"]
      description: '{{ doc("instance_sql_errors") }}'
      columns:
        - name: run_id
          data_tests:
            - not_null
        - name: sql_errors
          data_tests:
            - not_null
        - name: ping_date
          data_tests:
            - not_null
        - name: uploaded_at
          data_tests:
            - not_null
    - name: saas_usage_ping_namespace
      tags: ["product", "service_ping"]
      description: '{{ doc("saas_usage_ping_namespace") }}'
      columns:
        - name: saas_usage_ping_gitlab_dotcom_namespace_id
          data_tests:
            - not_null
            - unique:
                config:
                  where: "ping_date >= DATEADD('day',-3,CURRENT_DATE())"
    - name: instance_combined_metrics
      tags: ["product", "service_ping"]
      description: '{{ doc("instance_combined_metrics") }}'
      columns:
        - name: id
          data_tests:
            - not_null
            - unique
    - name: internal_events_ping_namespace
      tags: ["product", "service_ping"]
      description: '{{ doc("internal_events_ping_namespace") }}'
      columns:
        - name: saas_usage_ping_gitlab_dotcom_namespace_id
          data_tests:
            - not_null
            - unique:
                config:
                  where: "ping_date >= DATEADD('day',-3,CURRENT_DATE())"