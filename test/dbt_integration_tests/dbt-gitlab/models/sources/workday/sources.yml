version: 2

sources:
  - name: workday
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: workday
    loader: Airflow
    loaded_at_field: _fivetran_synced
    
    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: compensation
        description: '{{ doc("workday_compensation") }}'
        identifier: employee_compensation

      - name: custom_bonus
        description: '{{ doc("workday_custom_bonus") }}'
        identifier: employee_bonus

      - name: on_target_earnings
        description: '{{ doc("workday_on_target_earnings") }}'

      - name: directory
        description: '{{ doc("workday_directory") }}'
        identifier: employee_directory

      - name: emergency_contacts
        description: '{{ doc("workday_emergency_contacts") }}'
        identifier: employee_emergency_contacts

      - name: employee_mapping
        description: '{{ doc("workday_employee_mapping") }}'

      - name: employment_status
        description: '{{ doc("workday_employment_status") }}'

      - name: job_info
        description: '{{ doc("workday_job_info") }}'

      - name: supervisory_organization
        description: '{{ doc("supervisory_organization") }}'

      - name: performance_growth_potential
        description: '{{ doc("performance_growth_potential") }}'

      - name: all_workers
        description: '{{ doc("all_workers") }}'
      
      - name: staffing_history_approved
        description: '{{ doc("staffing_history_approved") }}'

      - name: gitlab_usernames
        description: '{{ doc("gitlab_usernames") }}'

      - name: assess_talent
        description: '{{ doc("assess_talent") }}'

      - name: job_profiles
        description: '{{ doc("job_profiles") }}'

      - name: cost_centers
        description: '{{ doc("cost_centers") }}'
    
      - name: time_off
      
  - name: workday_hcm
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: workday_hcm
    loader: Airflow
    loaded_at_field: _fivetran_synced
    
    quoting:
      database: true
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}
    
    tables:
      - name: organization
      - name: organization_hierarchy_detail
      - name: job_profile
      - name: job_family_job_profile
      - name: job_family
      - name: worker
      - name: person_name
