version: 2

sources:
  - name: dbt
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: dbt
    loader: airflow
    loaded_at_field: uploaded_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 24, period: hour}

    tables:
      - name: run
        description: This is for the results from any regular `dbt run`
        identifier: run_results
      - name: freshness
        description: This is for the results from `dbt source snapshot-freshness`
        identifier: sources
      - name: manifest
        description: This is the compiled dbt project along with packages
        identifier: manifest
      - name: gdpr_logs
        description: GDPR deletion logs
      - name: source_tests
        description: This is for the results from testing source data.
        identifier: source_tests_run_results
      - name: snapshots
        description: This is for the results from `dbt snapshot`
        identifier: snapshots_run_results
      - name: test
        description: This is for running dbt test on non-source models.
        identifier: test_run_results
