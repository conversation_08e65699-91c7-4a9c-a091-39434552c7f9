version: 2

models:
    - name: airflow_dag_source
      description: "Static information about airflow DAGs."
      columns:
        - name: dag_id
          data_tests:
            - not_null
            - unique
        - name: is_active
          data_tests:
            - not_null
        - name: is_paused
          data_tests:
            - not_null
        - name: schedule_interval
        
    - name: airflow_dag_run_source
      description: "Information about individual runs of a DAG."
      columns:
        - name: dag_id
          data_tests:
            - not_null
        - name: execution_date
          data_tests:
            - not_null
        - name: run_state
          data_tests:
            - not_null
        
        