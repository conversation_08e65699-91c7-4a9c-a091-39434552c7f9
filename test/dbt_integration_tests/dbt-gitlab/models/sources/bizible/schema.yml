version: 2
models:
  - name: bizible_account_to_emails_source
    columns:
      - name: account_to_email_id
        meta:
          sensitive: true
      - name: email
        meta:
          sensitive: true
  - name: bizible_accounts_source
    columns:
      - name: name
        meta:
          sensitive: true
  - name: bizible_activities_source
  - name: bizible_ad_accounts_source
  - name: bizible_ad_campaigns_source
  - name: bizible_ad_groups_source
  - name: bizible_ad_providers_source
  - name: bizible_ads_source
  - name: bizible_advertisers_source
  - name: bizible_attribution_touchpoints_source
    columns:
      - name: email
        meta:
          sensitive: true
      - name: user_touchpoint_id
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_campaign_members_source
    columns:
      - name: lead_email
        meta:
          sensitive: true
      - name: contact_email
        meta:
          sensitive: true
  - name: bizible_channels_source
  - name: bizible_contacts_source
    columns:
      - name: email
        meta:
          sensitive: true
  - name: bizible_conversion_rates_source
  - name: bizible_costs_source
  - name: bizible_creatives_source
  - name: bizible_crm_events_source
    columns:
      - name: lead_email
        meta:
          sensitive: true
      - name: contact_email
        meta:
          sensitive: true
  - name: bizible_crm_tasks_source
    columns:
      - name: lead_email
        meta:
          sensitive: true
      - name: contact_email
        meta:
          sensitive: true
  - name: bizible_currencies_source
  - name: bizible_customer_ab_tests_source
    columns:
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_email_to_visitor_ids_source
    columns:
      - name: email_to_visitor_id
        meta:
          sensitive: true
      - name: email
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_facts_source
    columns:
      - name: email
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_form_submits_source
    columns:
      - name: email
        meta:
          sensitive: true
      - name: ip_address
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_keywords_source
  - name: bizible_lead_stage_transitions_source
    columns:
      - name: email
        meta:
          sensitive: true
  - name: bizible_leads_source
    columns:
      - name: email
        meta:
          sensitive: true
  - name: bizible_opp_stage_transitions_source
    columns:
      - name: email
        meta:
          sensitive: true
  - name: bizible_opportunities_source
  - name: bizible_page_views_source
    columns:
      - name: email
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_placements_source
  - name: bizible_segments_source
  - name: bizible_site_links_source
  - name: bizible_sites_source
  - name: bizible_stage_definitions_source
  - name: bizible_touchpoints_source
    columns:
      - name: user_touchpoint_id
        meta:
          sensitive: true
      - name: email
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
  - name: bizible_urls_source
  - name: bizible_user_touchpoints_source
    columns:
      - name: user_touchpoint_id
        meta:
          sensitive: true
      - name: email
        meta:
          sensitive: true
      - name: visitor_id
        meta:
          sensitive: true
