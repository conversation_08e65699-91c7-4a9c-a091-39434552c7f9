version: 2

models:

  - name: mart_team_member_directory
    description: '{{ doc("mart_team_member_directory") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - employee_id
              - valid_from
    columns:
      - name: dim_team_member_sk
        description: A unique ID for each team member
        data_tests: 
          - not_null
      - name: dim_team_sk
        description: A unique ID for each team
      - name: employee_id
        description: Workday Employee Identification 
        data_tests: 
          - not_null
      - name: nationality
        description: Team member's nationality# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ethnicity
        description: Team member's ethnicity# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: first_name
        description: Team member's first name
      - name: last_name
        description: Team member's last name
      - name: full_name
        description: Team member's full name
      - name: gender
        description: Team member's gender# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: work_email
        description: Team member's work email
      - name: date_of_birth
        description: Team member's date of birth# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: age
        description: Team member's age# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: age_cohort
        description: Team member's age cohort# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: cost_center
        description: Team member's cost center
      - name: gitlab_username
        description: Team member's gitlab username
      - name: country
        description: Team member's country# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: region
        description: Team member's region# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: region_modified
        description: Region modified for team members from the Americas. Team members are split into LATAM or NORAM
      - name: gender_region
        description: Team member's gender and region combined# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ethnicity_region
        description: Team member's ethnicity and region combined# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: urg_group
        description: Team member's URG group# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: urg_region
        description: Team member's URG region# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: hire_date
        description: Team member's hire date
      - name: employee_type
        description: The employee type from the following list - Employee (PEO or Entity), Contractor (Independent, customer-to-customer or PEO), Intern, Consultant.
      - name: termination_date
        description: Team member's termination date
      - name: is_current_team_member
        description: Team member's team member status (are they a current team member or not)
      - name: is_rehire
        description: Team member's rehire flag
      - name: team_id
        description: A unique ID for each team from Workday
      - name: team_manager_name
        description: Team member's manager name
      - name: department
        description: Team member's department
      - name: division
        description: Team member's division
      - name: suporg
        description: Team member's Support Organization
      - name: job_code
        description: Team member's job code
      - name: position
        description: Team member's position
      - name: job_family
        description: Team member's job family
      - name: job_specialty_single
        description: Team member's job specialty
      - name: job_specialty_multi
        description: Team member's job specialty (multi-select)
      - name: management_level
        description: Team member's management level
      - name: job_grade
        description: Team member's job grade
      - name: entity
        description: Team member's entity# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: valid_from
      - name: valid_to
      - name: is_current

  - name: mart_team_member_absence
    description: '{{ doc("mart_team_member_absence") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
             - dim_team_member_sk
             - absence_date
             - pto_uuid
  
    columns:
      - name: dim_team_member_sk
        description: A unique ID for each team member
        data_tests: 
          - not_null
      - name: dim_team_sk
        description: A unique ID for each team
      - name: employee_id
        description: Workday Employee Identification 
        data_tests: 
          - not_null
      - name: nationality
        description: Team member's nationality# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ethnicity
        description: Team member's ethnicity# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: first_name
        description: Team member's first name
      - name: last_name
        description: Team member's last name
      - name: full_name
        description: Team member's full name
      - name: gender
        description: Team member's gender# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: work_email
        description: Team member's work email
      - name: date_of_birth
        description: Team member's date of birth# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: gitlab_username
        description: Team member's gitlab username
      - name: country
        description: Team member's country# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: region
        description: Team member's region# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: region_modified
        description: Region modified for team members from the Americas. Team members are split into LATAM or NORAM
      - name: gender_region
        description: Team member's gender and region combined# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ethnicity_region
        description: Team member's ethnicity and region combined# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: urg_group
        description: Team member's URG group# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: urg_region
        description: Team member's URG region# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: hire_date
        description: Team member's hire date
      - name: employee_type
        description: The employee type from the following list - Employee (PEO or Entity), Contractor (Independent, customer-to-customer or PEO), Intern, Consultant.
      - name: termination_date
        description: Team member's termination date
      - name: is_current_team_member
        description: Team member's team member status (are they a current team member or not)
      - name: is_rehire
        description: Team member's rehire flag
      - name: team_id
        description: A unique ID for each team from Workday
      - name: job_code
        description: The code of the team member's job position.
      - name: job_specialty_single
        description: The team member's job specialty.
      - name: job_specialty_multi
        description: The team member's job specialty (multi-select).
      - name: position
        description: The team member's job profile name.
      - name: job_family
        description: The team member's job family.
      - name: management_level
        description: The team member's management level.
      - name: job_grade
        description: The team member's job grade.
      - name: entity
        description: The team member's entity.# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: is_position_active
        description: Whether the team member's job position is active.
      - name: is_current_team_member_position
        description: Whether the team member's job position is the most current as of the date the query is run.
      - name: absence_start
        description: The team member's absence start date
        data_tests: 
          - not_null
      - name: absence_end
        description: The team member's absence end date
      - name: absence_date  
        description: The team member's day of absence   
        data_tests: 
          - not_null
      - name: is_pto
        description: If this absence day overlaps with a PTO.# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: is_holiday
        description: If this absence day overlaps with a holiday.
      - name: pto_uuid
        description: Unique identifier for this pto event
      - name: pto_type_uuid
        description: The UUID of this OOO Type.# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: pto_group_type
        description: The group type of the absence.# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: pto_status
        description: Enum:"AP" "RQ" "DN" "CN" 2-character enumeration denoting the status of the OOO Event.
          "AP" = Approved  "RQ" = Requested  "DN" = Denied  "CN" = Cancelled
      - name: pto_status_name
        description: The decoded pto_status
      - name: total_hours
        description: How long the user was OOO for. This is different from recorded_hours in that it is unaware of holidays and weekends.
      - name: recorded_hours
        description: How many hours were recorded as OOO for this event.
      - name: absence_status
        description: The team member's absence status.# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: employee_day_length
        description: How long this User's "day" is in hours

