{% docs wk_gainsight_account_scorecard_history %}
This object is used to store the historical scores of the measures in the Scorecards module. 
For example: C360 and Habits Reports.
{% enddocs %}

{% docs wk_gainsight_activity_attendee %}	 
This object is used as Timeline Activity in Reporting and Rules Engine modules. 
{% enddocs %}

{% docs wk_gainsight_activity_comments %}	
It enables CSMs to view or add any additional information about a person such as personal interests/hobbies, temperament, current concerns, etc. 
You can capture unique characteristics of a Person (such as conversation tips), to help you or colleagues in future conversations with that individual 
in the newly introduced Comments field of the C360 Person section.
{% enddocs %}

{% docs wk_gainsight_activity_timeline %}	 	 
This object is used as Timeline Activity in C360/R360 and Email Assist Tasks.
For example: reports based on Timeline Activities and associated information fetched from other objects.
{% enddocs %}

{% docs wk_gainsight_ao_advanced_outreach_company %}	 
This object is used to store important information about Programs. 
For example: create reports on important parameters of Programs.
{% enddocs %}

{% docs wk_gainsight_call_to_action %}	 
This object is used in Cockpit and Call to Action (CTA). 
For example: Rules/Reports on CTAs and Success Plans (complete or incomplete, overdue, etc).
{% enddocs %}

{% docs wk_gainsight_comments %}
It enables CSMs to view or add any additional information about a person such as personal interests/hobbies, temperament, current concerns, etc. 
You can capture unique characteristics of a Person (such as conversation tips), to help you or colleagues in future conversations with that individual 
in the newly introduced Comments field of the C360 Person section.
{% enddocs %}

{% docs wk_gainsight_companies_and_ebrs %}	 
This object stores the information about the status of the companies that got added during the configuration.
{% enddocs %}

{% docs wk_gainsight_companies_with_success_plan_details %}	 
This object is used in Success Plans. 
For example: Rules/Reports on Success Plans (complete or incomplete, overdue, etc).
{% enddocs %}

{% docs wk_gainsight_companies_with_success_plan_objectives %}
This object is used in Success Plans. 
For example: Rules/Reports on Success Plans (complete or incomplete, overdue, etc).
{% enddocs %}

{% docs wk_gainsight_company %}	      
This object stores information about the history of how many companies got added on any given day and how many companies got removed on any day.
{% enddocs %}

{% docs wk_gainsight_company_person %}
Company Person is a standard object that stores attributes of the people in association with a company. 
Lets consider John Damon works at Acme, his attributes while working at Acme that includes Company name, 
Title/Designation, Office phone, Manager, etc. are stored in this object.
{% enddocs %}

{% docs wk_gainsight_cs_task %}	 	   
This object is used in Calls to Action and CS Tasks. 
For example: Reports on the CS Tasks (complete or incomplete, overdue, etc).
{% enddocs %}

{% docs wk_gainsight_csat_survey_response %}	 	
This object is used to store information about responses to `CSAT` Survey questions in the Surveys 2.0 module.
{% enddocs %}

{% docs wk_gainsight_ctas_healthscores %}	 
Health score of a customer. This is overall health score of the current scorecard for a company.
{% enddocs %}

{% docs wk_gainsight_customer_health_scorecard_fact_1 %}	 	
Health score of a customer. This is overall health score of the current scorecard for a company.
{% enddocs %}

{% docs wk_gainsight_email_logs %}	
Email Log contains an aggregated entry for a single recipient email. 
There will be only one entry in the Email Log for a recipient email irrespective of the number of events that occur for that email.

This object is used in Journey Orchestrator, Advanced Outreaches (Programs), Email Assist. 
For example: Rules/Reports based on success of email campaigns, number of Email Assist tasks sent, types of error messages being returned by systems, etc.
{% enddocs %}

{% docs wk_gainsight_nps_survey_response %}	    
This object is used to store information about an NPS® Survey and its Response in the Surveys module. 
{% enddocs %}

{% docs wk_gainsight_success_plan %}	 	
This object is used in Success Plans. 
For example: Rules/Reports on Success Plans (complete or incomplete, overdue, etc).
{% enddocs %}

{% docs wk_gainsight_user_sfdcgitlabproduction %}	 
Data in all the fields are synced from SFDC.
{% enddocs %}


{% docs wk_gainsight_opt_out_emails %}	 
This object is used in Journey Orchestrator module. For example; stores information about opt-out emails.
**Note:** Gainsight recommends users to use Opt-Out Emails instead of Unsubscribed emails.
{% enddocs %}

{% docs wk_gainsight_advanced_outreach_participants %}	 
This object is used to store information about the Participants in Programs. 
For example: to retrieve information about the participants in a Program through Report Builder.
{% enddocs %}

{% docs wk_gainsight_advanced_outreach_emails %}	 
This object is used to store information about emails in Programs. 
For example: to create a report on the information about emails sent by Programs.
{% enddocs %}

{% docs wk_gainsight_advanced_outreach_cta %}	 
This object is used in Journey Orchestrator module. 
For example: stores information about CTAs Triggering from Programs.
{% enddocs %}

{% docs wk_gainsight_advanced_outreach_participant_activity %}	 
This object is used in Analytics on Programs. 
For example: to retrieve analytics on Program Participant Activities through Report Builder.
{% enddocs %}

{% docs wk_gs_persona_healthy_activity %}	 
This object is used to calculate healthy activity counts for gainsight personas. 
{% enddocs %}