version: 2

models:
  - name: wk_sales_clari_net_arr_forecast
    description: Forecast table combining the different API sub-payloads together
    columns:
      - name: forecast_id
        data_tests:
          - not_null
      - name: user_full_name
        data_tests:
          - not_null
      - name: user_email
        data_tests:
          - not_null
      - name: crm_user_id
        data_tests:
          - not_null
      - name: sales_team_role
        data_tests:
          - not_null
      - name: parent_role
      - name: fiscal_quarter
        data_tests:
          - not_null
      - name: field_name
        data_tests:
          - not_null
      - name: week_number
        data_tests:
          - not_null
      - name: week_start_date
        data_tests:
          - not_null
      - name: week_end_date
        data_tests:
          - not_null
      - name: field_type
        data_tests:
          - not_null
      - name: forecast_value
      - name: is_updated
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - forecast_id
            - fiscal_quarter
            - week_start_date
            - crm_user_id
            - field_name
  - name: wk_rpt_crm_opportunity_reduced
    description: a table with reduced column list from mart_crm_opportunity that is used for testing entitlement tables for Tableau
    columns:
      - name: dim_crm_opportunity_id
        data_tests:
            - not_null
            - unique
  - name: wk_rpt_crm_account_reduced
    description: a table with reduced column list from mart_crm_account that is used for testing entitlement tables for Tableau
    columns:
      - name: dim_crm_account_id
        data_tests:
            - not_null
            - unique
  - name: wk_prep_crm_account_share_active
    description: an active account share table with reduced column list
    columns:
      - name: account_id
        data_tests:
            - not_null
  - name: wk_prep_crm_opportunity_share_active
    description: an active opportunity share table with reduced column list
    columns:
      - name: opportunity_id
        data_tests:
            - not_null
  - name: wk_crm_account_entitlement
    description: an account entitlment table to be used for row level security in Tableau
    columns:
      - name: account_id
  - name: wk_crm_opportunity_entitlement
    description: an opportunity entitlment table to be used for row level security in Tableau
    columns:
      - name: opportunity_id
  - name: wk_sales_daily_case_automation_test
    description: >
      This is the test model for the PUMP version so we can test changes to the case creation logic.
  - name: wk_sales_gds_account_snapshots
    description: >
      Account snapshots with business logic specific to the Global Digital SMB Sales team
  - name: wk_sales_gds_cases
    description: >
      SFDC cases with Global Digital SMB business logic including activity summary
  - name: wk_sales_gds_fo_buckets
    description: >
      Derives the source of first orders using namespace and trial information
  - name: wk_sales_gds_forecast_consolidated
    description: >
      Monthly forecast model for the Global Digital SMB team

  - name: wk_sales_sfdc_opportunity_product_snapshot
    description: >
      This references the snapshots of the SFDC opportunity products snapshot source table

  - name: wk_sales_product_price_quantity
    description: >
      Current price and quantity information based on ARR that can be joined to opportunities
  - name: wk_sales_renewal_arr
    description: >
      Pre- and post-renewal price and quantity information based on ARR to be joined to opportunities
  - name: wk_sales_gds_opportunities
    description : >
      Opportunity data with SMB-specific business logic
  - name: wk_sales_gds_account_disposition
    description : >
      Account data with appended summaries of recent cases and opportunities
    data_tests: 
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - dim_crm_account_id
              - snapshot_id
              - sub_subscription_id
              - dim_crm_opportunity_id_next_renewal
  - name: wk_sales_sfdc_opportunity_product
    description: Salesforce opportunity products workspace table. [Link to Epic](https://gitlab.com/groups/gitlab-com/business-technology/enterprise-apps/-/epics/527)
  - name: bvs_ecosystems_document
    description: '{{ doc("bvs_ecosystems_document") }}'
    columns:
      - name: account_name
        description: '{{ doc("bvs_ecosystems_account_name") }}'
      - name: collaborators
        description: '{{ doc("bvs_ecosystems_collaborators") }}'
      - name: crm_account_id
        description: '{{ doc("bvs_ecosystems_crm_account_id") }}'
      - name: crm_account_name
        description: '{{ doc("bvs_ecosystems_crm_account_name") }}'
      - name: crm_account_owner_email
        description: '{{ doc("bvs_ecosystems_crm_account_owner_email") }}'
      - name: crm_account_phone
        description: '{{ doc("bvs_ecosystems_crm_account_phone") }}'
      - name: crm_opportunity_id
        description: '{{ doc("bvs_ecosystems_crm_opportunity_id") }}'
      - name: crm_opportunity_name
        description: '{{ doc("bvs_ecosystems_crm_opportunity_name") }}'
      - name: crm_opportunity_owner_email
        description: '{{ doc("bvs_ecosystems_crm_opportunity_owner_email") }}'
      - name: crm_opportunity_phone
        description: '{{ doc("bvs_ecosystems_crm_opportunity_phone") }}'
      - name: is_demo
        description: '{{ doc("bvs_ecosystems_is_demo") }}'
      - name: document_name
        description: '{{ doc("bvs_ecosystems_document_name") }}'
      - name: document_status
        description: '{{ doc("bvs_ecosystems_document_status") }}'
      - name: document_id
        description: '{{ doc("bvs_ecosystems_document_id") }}'
      - name: update_history_created_at
        description: '{{ doc("bvs_ecosystems_update_history_created_at") }}'
      - name: update_history_created_by_id
        description: '{{ doc("bvs_ecosystems_update_history_created_by_id") }}'
      - name: update_history_created_by_username
        description: '{{ doc("bvs_ecosystems_update_history_created_by_username") }}'
      - name: update_history_last_updated_at
        description: '{{ doc("bvs_ecosystems_update_history_last_updated_at") }}'
      - name: update_history_updated_by_id
        description: '{{ doc("bvs_ecosystems_update_history_updated_by_id") }}'
      - name: update_history_updated_by_username
        description: '{{ doc("bvs_ecosystems_update_history_updated_by_username") }}'
      - name: uploaded_at
        description: '{{ doc("bvs_ecosystems_uploaded_at") }}'
  - name: bvs_ecosystems_collaborative_value_assessment
    description: '{{ doc("bvs_ecosystems_cva") }}'
    columns:
      - name: account_name
        description: '{{ doc("bvs_ecosystems_account_name") }}'
      - name: active_external_collaborator
        description: '{{ doc("bvs_ecosystems_active_external_collaborator") }}'
      - name: active_internal_collaborator
        description: '{{ doc("bvs_ecosystems_active_internal_collaborator") }}'
      - name: active_partner_collaborator
        description: '{{ doc("bvs_ecosystems_active_partner_collaborator") }}'
      - name: crm_opportunity_id
        description: '{{ doc("bvs_ecosystems_crm_opportunity_id") }}'
      - name: collaborators
        description: '{{ doc("bvs_ecosystems_collaborators") }}'
      - name: count_of_value_drivers_modified
        description: '{{ doc("bvs_ecosystems_count_of_value_drivers_modified") }}'
      - name: is_discovery_modified
        description: '{{ doc("bvs_ecosystems_is_discovery_modified") }}'
      - name: document_id
        description: '{{ doc("bvs_ecosystems_document_id") }}'
      - name: effectiveness_score
        description: '{{ doc("bvs_ecosystems_effectiveness_score") }}'
      - name: does_include_discovery
        description: '{{ doc("bvs_ecosystems_does_include_discovery") }}'
      - name: is_investment_modified
        description: '{{ doc("bvs_ecosystems_is_investment_modified") }}'
      - name: is_demo
        description: '{{ doc("bvs_ecosystems_is_demo") }}'
      - name: updated_at,
        description: '{{ doc("bvs_ecosystems_updated_at") }}'
      - name: latest_editor
        description: '{{ doc("bvs_ecosystems_latest_editor") }}'
      - name: models_edits
        description: '{{ doc("bvs_ecosystems_models_edits") }}'
      - name: presentation_name
        description: '{{ doc("bvs_ecosystems_presentation_name") }}'
      - name: viewer_logs
        description: '{{ doc("bvs_ecosystems_viewer_logs") }}'
      - name: uploaded_at
        description: '{{ doc("bvs_ecosystems_uploaded_at") }}'
  - name: bvs_ecosystems_document_collaborator
    description: '{{ doc("bvs_ecosystems_document_collaborator") }}'
    columns:
      - name: document_id
        description: '{{ doc("bvs_ecosystems_document_id") }}'
      - name: access_level
        description: '{{ doc("bvs_ecosystems_access_level") }}'
      - name: is_document_visible
        description: '{{ doc("bvs_ecosystems_is_document_visible") }}'
      - name: collaborator_id
        description: '{{ doc("bvs_ecosystems_collaborator_id") }}'
      - name: user_id
        description: '{{ doc("bvs_ecosystems_user_id") }}'
      - name: user_name
        description: '{{ doc("bvs_ecosystems_user_name") }}'