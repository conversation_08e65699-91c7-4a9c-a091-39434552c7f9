version: 2

models:
  - name: wk_rpt_ai_gateway_events_flattened_with_features
    tags: ["exception", "product"]
    description: Snowplow events coming from the AI Gateway, flattened to the feature enabled by namespace grain (1 row per namespace that allowed the event to occur) and joined to features via the cloud connector configuration file. This means the grain of this model is 1 row per namespace that allowed the event to occur and feature associated with the event. Multiple features can be associated with each unit primitive request.
    columns:
      - name: behavior_structured_event_pk
        description: '{{ doc("behavior_structured_event_pk") }}'
      - name: dim_behavior_event_sk
        description: '{{ doc("dim_behavior_event_sk") }}'
      - name: dim_app_release_major_minor_sk
        description: '{{ doc("dim_app_release_major_minor_sk") }}'
      - name: dim_installation_id
        description: '{{ doc("dim_installation_id") }}'
      - name: gsc_feature_enabled_by_namespace_ids
        description: '{{ doc("gsc_feature_enabled_by_namespace_ids") }}'
      - name: enabled_by_namespace_id
        description: Singular namespace taken from a list of namespaces that allow the user to use the tracked feature. This list does not have to be 1:1 with the event and does not necessarily correspond to where the event took place.
      - name: enabled_by_ultimate_parent_namespace_id
        description: Ultimate parent namespace of the namespace that enabled this feature.
      - name: behavior_at
        description: '{{ doc("behavior_at") }}'
      - name: dim_instance_id
        description: '{{ doc("dim_instance_id") }}'
      - name: host_name
        description: '{{ doc("host_name") }}'
      - name: enabled_by_internal_installation
        description: Event was enabled by a GitLab internal installation
      - name: enabled_by_internal_namespace
        description: Event was enabled by a GitLab internal namespace
      - name: enabled_by_product_delivery_type
        description: Event was enabled by a SaaS or Self-Managed subscription
      - name: enabled_by_product_deployment_type
        description: vent was enabled by a deployment type of GitLab to include either GitLab.com, Dedicated or Self-Managed.
      - name: gitlab_global_user_id
        description: '{{ doc("gitlab_global_user_id") }}'
      - name: app_id
        description: '{{ doc("app_id") }}'
      - name: contexts
        description: '{{ doc("contexts") }}'
      - name: gitlab_standard_context
        description: '{{ doc("gitlab_standard_context") }}'
      - name: gsc_environment
        description: '{{ doc("gsc_environment") }}'
      - name: gsc_source
        description: '{{ doc("gsc_source") }}'
      - name: delivery_type
        description: '{{ doc("product_delivery_type") }}'
      - name: gsc_correlation_id
        description: '{{ doc("gsc_correlation_id") }}'
      - name: gsc_extra
        description: '{{ doc("gsc_extra") }}'
      - name: gsc_instance_version
        description: '{{ doc("gsc_instance_version") }}'
      - name: enabled_by_major_minor_version_at_event_time
        description:  '{{ doc("major_minor_version") }}'
      - name: enabled_by_major_minor_version_num_at_event_time
        description: '{{ doc("major_minor_version_num") }}'
      - name: user_country
        description: '{{ doc("user_country") }}'
      - name: user_timezone_name
        description: '{{ doc("user_timezone_name") }}'
      - name: event_value
        description: '{{ doc("event_value") }}'
      - name: event_category
        description: '{{ doc("event_category") }}'
      - name: event_action
        description: '{{ doc("event_action") }}'
      - name: event_label
        description: '{{ doc("event_label") }}'
      - name: clean_event_label
        description: '{{ doc("clean_event_label") }}'
      - name: event_property
        description: '{{ doc("event_property") }}'
      - name: unit_primitive
        description: The lowest grain of a feature that is tracked.
      - name: enabled_by_dim_subscription_ids_at_event_time
        description: Based on the `gsc_feature_enabled_by_namespace_ids`, this event was tied a namespace, which was then mapped to its subscritpion_id(s) at the time of the event.
      - name: enabled_by_dim_subscription_ids_original_at_event_time
        description: Based on the `gsc_feature_enabled_by_namespace_ids`, this event was tied a namespace, which was then mapped to its original subscritpion_id(s) at the time of the event.
      - name: enabled_by_dim_crm_account_id_at_event_time
        description: Based on the `gsc_feature_enabled_by_namespace_ids`, this event was tied a namespace, which was then mapped to its subscritpion(s) at the time of the event, which can then be associated with a CRM account id.
      - name: enabled_by_crm_account_name_at_event_time
        description:  Based on the `gsc_feature_enabled_by_namespace_ids`, this event was tied a namespace, which was then mapped to its subscritpion(s) at the time of the event, which can then be associated with a CRM account name.
      - name: enabled_by_product_tier_names_at_event_time
        description: The list of product tiers associated with the namespace that enabled this event.
      - name: enabled_by_product_at_event_time
        description: The list of tiered products associated with the namespace that enabled this event.
      - name: enabled_by_add_on_dim_subscription_id_at_event_time
        description: The add on subscription_id associated with the namespace that enabled this event.
      - name: enabled_by_add_on_product_at_event_time
        description: The add on product associated with the namespace that enabled this event.
      - name: enabled_by_add_on_trial_product_at_event_time
        description: The add on trial product associated with the namespace that enabled this event.
      - name: enabled_by_oss_or_edu_rate_plan_at_event_time
        description: Flag to indicate if this event was enabled by an EDU/OSS rate plan at the time of the event.
      - name: enabled_by_product_entity_id
        description: A 'product entity' is an Ultimate Parent Namespace (GitLab.com) or a GitLab Installation (Self-Managed and Dedicated).
      - name: enabled_by_product_entity_type
        description: ultimate_parent_namespace_id (GitLab.com product entities) or dim_installation_id (Self-Managed and Dedicated).
      - name: enabled_by_internal_product_entity
        description: Usage enabled by a product entity that is flagged as either an internal namespace or internal installation
      - name: enabled_by_product_tier
        description:  The list of product tiers associated with the namespace that enabled this event. Transformed to a string format from enabled_by_product_tier_names_at_event_time for easier consumption in Tableau.
      - name: duo_subscription_clean
        description: Displaying only Duo products from enabled_by_add_on_product_at_event_time.
      - name: enabled_by_duo_add_on_detail
        description: Detailed combination of Duo Subscription and Duo Trial products present at time of event.
      - name: enabled_by_duo_add_on
        description: Simplified categorization of Duo entitlements as 'Duo Subscription' 'Duo Trial' and 'None'
      - name: gsc_is_gitlab_team_member
        description: ' {{ doc("gsc_is_gitlab_team_member") }}'
      - name: feature_category_at_event_time
        description: ' {{ doc("feature_category") }} '
      - name: feature_category
        description: ' {{ doc("cloud_connector_feature_category") }} '
      - name: engineering_group
        description: ' {{ doc("cloud_connector_engineering_group") }} '
      - name: backend_services
        description: ' {{ doc("cloud_connector_backend_services") }} '