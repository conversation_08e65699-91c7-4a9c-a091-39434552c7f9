version: 2

models:
  - name: map_bizible_marketing_channel_path
    description: '{{ doc("map_bizible_marketing_channel_path") }}'
    columns:
      - name: bizible_marketing_channel_path
        description: Channel path to map
        data_tests:
          - not_null
      - name: bizible_marketing_channel_path_name_grouped
        description: Mapped marketing channel name.

  - name: map_bizible_campaign_grouping
    description: '{{ doc("map_bizible_campaign_grouping") }}'
    columns:
      - name: bizible_campaign_grouping_id
        data_tests:
          - not_null
          
  - name: map_gitlab_dotcom_xmau_metrics
    description: '{{ doc("map_gitlab_dotcom_xmau_metrics") }}'
    columns:
      - name: common_events_to_include
        data_tests:
          - unique
          - not_null

  - name: map_ip_to_country
    description: '{{ doc("map_ip_to_country") }}'
    columns:
      - name: ip_address_hash
        data_tests:
          - unique
          - not_null
      - name: location_id

  - name: map_moved_duplicated_issue
    description: '{{ doc("map_moved_duplicated_issue") }}'
    columns:
      - name: issue_id
        data_tests:
          - unique
          - not_null
      - name: dim_issue_sk
        data_tests:
          - not_null

  - name: map_license_subscription_account
    description: '{{ doc("map_license_subscription_account") }}'
    columns:
      - name: dim_license_id
      - name: license_md5
      - name: license_sha256
      - name: dim_subscription_id
      - name: dim_crm_account_id
      - name: ultimate_parent_account_id

  - name: map_product_tier
    description: '{{ doc("map_product_tier") }}'
    columns:
      - name: product_rate_plan_id
        description: "Rate Plan ID for Zuora Product"
        data_tests:
          - not_null
          - unique
        tags: ["tdf", "common", "gainsight"]
      - name: product_rate_plan_name
        description: "rate plan name from Zuora product source"
      - name: product_tier_historical
        description: Product pricing tiers prior to [tier renaming](https://gitlab-com.gitlab.io/packaging-and-pricing/pricing-handbook/handbook/renaming_tiers/) (early 2021).
        data_tests:
          - not_null
        tags: ["tdf", "common", "gainsight"]
      - name: product_delivery_type
        description: "How product is delivered to customer; SaaS or Self-Managed (or Other)"
      - name: product_deployment_type
        description: How a product is deployed to the customer; Self-Managed, GitLab.com or Dedicated (or Other).
      - name: product_ranking
        description: "Numeric product pricing tier, independent of delivery type"
      - name: product_tier
        description: Product [pricing tiers](https://about.gitlab.com/handbook/marketing/strategic-marketing/tiers/) for both SaaS and Self-Managed customers. Includes the mapping from SaaS - Silver to SaaS - Premium, and from SaaS - Gold to SaaS - Ultimate.
        data_tests:
          - not_null
        tags: ["tdf", "common", "gainsight"]
      - name: product_category
        description: Categorizes Zuora products as either Add On Services or Base Products.


  - name: map_namespace_internal
    description: '{{ doc("map_namespace_internal") }}'
    columns:
      - name: ultimate_parent_namespace_id
        description: "The ultimate parent namespace identifiers that are internal to gitlab."
        data_tests:
          - not_null
          - unique

  - name: map_team_member_bamboo_gitlab_dotcom_gitlab_ops
    description: '{{ doc("map_team_member_bamboo_gitlab_dotcom_gitlab_ops") }}'
    columns:
      - name: bamboohr_employee_id
        description: "GitLab Team Member Employee ID as shown in BambooHR. Can be used to join to employee_directory_analysis."
      - name: bamboohr_full_name
        description: "GitLab Team Member Full Name as shown in BambooHR"
      - name: bamboohr_gitlab_email
        description: "GitLab Team Member GitLab Email as shown in BambooHR. Can be used to join to employee_directory_analysis."
      - name: gitlab_dotcom_user_id
        description: "GitLab Team Member GitLab.com SaaS Database user_id. Can be used to join onto any user id field in the gitlab_dotcom data set "
      - name: gitlab_dotcom_user_name
        description: "GitLab Team Member GitLab.com SaaS Database user_name. Can be used to join onto any user name field in the gitlab_dotcom data set "
      - name: gitlab_ops_user_id
        description: "GitLab Team Member ops.gitlab.net SaaS Database user_id. Can be used to join onto any user id field in the gitlab_ops data set "
      - name: gitlab_ops_user_name
        description: "GitLab Team Member ops.gitlab.net SaaS Database user_name. Can be used to join onto any user name field in the gitlab_ops data set "

  - name: map_subscription_lineage
    description: This models takes the `prep_subscription_intermediate` model and joins it to the `prep_subscription_lineage` model. The end result is every subscription is linked to its lineage (or null).
    columns:
      - name: dim_subscription_id
        data_tests:
          - not_null
          - unique
      - name: subscription_lineage
        description: "All the subscriptions subsquent to the primary subscription, separated by commas."
      - name: oldest_subscription_in_cohort
      - name: subscription_cohort_month
        data_tests:
          - not_null
      - name: subscription_cohort_quarter
        data_tests:
          - not_null
      - name: subscription_cohort_year
        data_tests:
          - not_null

  - name: map_subscription_opportunity
    description: '{{ doc("map_subscription_opportunity") }}'
    columns:
      - name: dim_subscription_id
        data_tests:
          - not_null
          - unique
      - name: dim_crm_opportunity_id
        data_tests:
          - not_null

  - name: map_ci_runner_project
    description: '{{ doc("map_ci_runner_project") }}'
    columns:
      - name: dim_ci_runner_project_id
        data_tests:
          - not_null
          - unique
      - name: dim_ci_runner_id
        data_tests:
          - not_null
      - name: dim_project_id
        data_tests:
          - not_null

  - name: map_team_member_user
    description: '{{ doc("map_team_member_user") }}'
    columns:
      - name: dim_team_member_sk
        data_tests:
          - not_null
          - unique
      - name: dim_user_sk
        data_tests:
          - not_null
          - unique
      - name: employee_id
      - name: user_id
      - name: gitlab_username
      - name: notification_email

  - name: map_latest_subscription_namespace_monthly
    description: '{{ doc("map_latest_subscription_namespace_monthly") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - dim_namespace_id
            - date_month

  - name: map_alternative_lead_demographics
    description: '{{ doc("map_alternative_lead_demographics") }}'
    columns:
      - name: dim_crm_person_id
        description: dim_crm_person_id from dim_crm_person
      - name: employee_count_segment_custom
        description: A case statement assigning segment based on the employee count from the first in the order of; Account Demographics Employee Count, Zoominfo Company Employee Count, Cognism Employee Count. Pubsec is always taken from Lean Data Matched Account Sales Segement
      - name: employee_bucket_segment_custom
        description: A case statement assigning segment based on the `employee_bucket` dim_crm_person. Pubsec is always taken from Lean Data Matched Account Sales Segement
      - name: geo_custom
        description: A case statement assigning geo according the [FY23 Territories Mapping File - SSoT](https://docs.google.com/spreadsheets/d/1gElhORjqraKDMQnWzApPelyP_vVa24tAOA85vb5f3Uc/edit#gid=**********) using `first_country` from dim_crm_person

  - name: map_project_internal
    description: '{{ doc("map_project_internal") }}'
    columns:
      - name: project_id
        description: '{{ doc("dim_project_id") }}' 
        data_tests:
          - not_null
          - unique
      - name: ultimate_parent_namespace_id
        description: '{{ doc("dim_ultimate_parent_namespace_id") }}' 
      - name: parent_namespace_id
        description: The namespace id that the project is directly related to.
      - name: project_namespace_id
        description: The namespace_id that represents the project

  - name: map_epic_internal
    description: '{{ doc("map_epic_internal") }}'
    columns:
      - name: ultimate_parent_namespace_id
        description: '{{ doc("dim_ultimate_parent_namespace_id") }}' 
      - name: namespace_id
        description: The namespace id that the epic is directly related to.
        data_tests: 
          - not_null
      - name: epic_id
        description: The namespace_id that represents the epic

  - name: map_team_member_snowflake_user
    description: A table to map current and former team members to there employee id
    columns:
      - name: employee_id
        data_tests:
          - unique
      - name: snowflake_user_name
        description: '{{ doc("snowflake_user_name") }}' 

  - name: map_namespace_subscription_product
    description: '{{ doc("map_namespace_subscription_product") }}'
    columns:
      - name: primary_key
        data_tests:
          - unique
          - not_null
      - name: date_actual
      - name: dim_subscription_id
        description: '{{ doc("dim_subscription_id") }}'
      - name: dim_subscription_id_original
        description: '{{ doc("dim_subscription_id_original") }}'
      - name: dim_namespace_id
        description: '{{ doc("dim_namespace_id") }}'
      - name: dim_crm_account_id
        description: '{{ doc("dim_crm_account_id") }}'
      - name: subscription_version
        description: '{{ doc("subscription_version") }}'
      - name: dim_product_detail_id
        description: '{{ doc("dim_product_detail_id") }}'
      - name: charge_type
        description: '{{ doc("charge_type") }}'
      - name: is_current_date
        description: '{{ doc("is_current_date") }}'

  - name: map_installation_subscription_product
    description: '{{ doc("map_namespace_subscription_product") }}'
    columns:
      - name: primary_key
        data_tests:
          - unique
          - not_null
      - name: date_actual
      - name: dim_subscription_id
        description: '{{ doc("dim_subscription_id") }}'
      - name: dim_subscription_id_original
        description: '{{ doc("dim_subscription_id_original") }}'
      - name: dim_installation_id
        description: '{{ doc("dim_installation_id") }}'
      - name: dim_crm_account_id
        description: '{{ doc("dim_crm_account_id") }}'
      - name: subscription_version
        description: '{{ doc("subscription_version") }}'
      - name: dim_product_detail_id
        description: '{{ doc("dim_product_detail_id") }}'
      - name: charge_type
        description: '{{ doc("charge_type") }}'
      - name: is_current_date
        description: '{{ doc("is_current_date") }}'