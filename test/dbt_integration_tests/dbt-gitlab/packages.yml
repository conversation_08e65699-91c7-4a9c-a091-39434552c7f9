# Update docs-packages.yml as well to have the docs site be accurate.
packages:
  # - git: **************:gitlab-data/snowflake_spend.git
  #   revision: "updates_for_dbt_1_1"
  #   warn-unpinned: false
  # - git: **************:gitlab-data/data-tests.git
  #   revision: "{{env_var('DATA_TEST_BRANCH')}}"
    # warn-unpinned: false
  - package: dbt-labs/audit_helper
    version: 0.9.0
  - package: dbt-labs/dbt_utils
    version: 1.1.1
  - package: dbt-labs/snowplow
    version: 0.15.1
  - package: dbt-labs/dbt_external_tables
    version: 0.8.7
  - package: brooklyn-data/dbt_artifacts
    version: 2.8.0

