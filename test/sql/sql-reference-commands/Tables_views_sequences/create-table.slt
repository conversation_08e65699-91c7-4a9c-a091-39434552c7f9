statement ok
CREATE OR <PERSON><PERSON>LACE TABLE mytable_1 (amount NUMBER);

exclude-from-coverage
statement ok
INSERT INTO mytable_1 VALUES(1);

query T
SELECT table_name
FROM information_schema.tables
WHERE table_name = 'MYTABLE_1';
----
MYTABLE_1

query TTTTTTTTTTTT
DESC TABLE MYTABLE_1;
----
AMOUNT	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL

statement ok
CREATE OR REPLACE TABLE example_2 (col1 NUMBER COMMENT 'a column comment') COMMENT='a table comment';

query T
SELECT comment
FROM information_schema.tables
WHERE table_name = 'EXAMPLE_2';
----
a table comment

query TTTTTTTTTTTT
DESC TABLE example_2;
----
COL1	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	a column comment	NULL	NULL

statement ok
CREATE OR REPLACE TABLE MYTABLE_1_copy (b) AS SELECT * FROM MYTABLE_1;

query TTTTTTTTTTTT
DESC TABLE MYTABLE_1_copy;
----
B	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL

statement ok
CREATE OR REPLACE TABLE MYTABLE_1_copy2 AS SELECT b+1 AS c FROM MYTABLE_1_copy;

query TTTTTTTTTTTT
DESC TABLE MYTABLE_1_copy2;
----
C	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL

query T
SELECT * FROM MYTABLE_1_copy2;
----
2

statement ok
CREATE OR REPLACE TABLE testtable (name VARCHAR, amount1 NUMBER, amount2 NUMBER);

statement ok
CREATE OR REPLACE TABLE testtable_summary (name, summary_amount) AS SELECT name, amount1 + amount2 FROM testtable;

statement ok
CREATE OR REPLACE TABLE MYTABLE_1 (amount NUMBER);

statement ok
INSERT INTO MYTABLE_1 VALUES(1);

query T
SELECT * FROM MYTABLE_1;
----
1

statement ok
CREATE OR REPLACE TABLE mytable_2 LIKE mytable;

query TTTTTTTTTTTT
DESC TABLE mytable_2;
----
AMOUNT	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL

query T
SELECT * FROM mytable_2;
----

statement ok
CREATE OR REPLACE TABLE collation_demo (
  uncollated_phrase VARCHAR, 
  utf8_phrase VARCHAR COLLATE 'utf8',
  english_phrase VARCHAR COLLATE 'en',
  spanish_phrase VARCHAR COLLATE 'es');

exclude-from-coverage
statement ok
INSERT INTO collation_demo (
      uncollated_phrase, 
      utf8_phrase, 
      english_phrase, 
      spanish_phrase) 
   VALUES (
     'pinata', 
     'pinata', 
     'pinata', 
     'piñata');

statement ok
CREATE TEMPORARY TABLE demo_temporary (i INTEGER);

statement ok
CREATE TEMP TABLE demo_temp (i INTEGER);

statement ok
CREATE LOCAL TEMPORARY TABLE demo_local_temporary (i INTEGER);

statement ok
CREATE LOCAL TEMP TABLE demo_local_temp (i INTEGER);

statement ok
CREATE GLOBAL TEMPORARY TABLE demo_global_temporary (i INTEGER);

statement ok
CREATE GLOBAL TEMP TABLE demo_global_temp (i INTEGER);

statement ok
CREATE VOLATILE TABLE demo_volatile (i INTEGER);

statement ok
CREATE OR REPLACE TABLE my_table(a INT);

statement ok
CREATE OR REPLACE TABLE my_table(
    a INT PRIMARY KEY,
    b VARCHAR(200)
  )
  DATA_RETENTION_TIME_IN_DAYS = 0
  DEFAULT_DDL_COLLATION = 'de';

statement ok
CREATE OR REPLACE TABLE my_table(
    a INT PRIMARY KEY,
    c VARCHAR(200)
  )
  DEFAULT_DDL_COLLATION = 'de';

statement ok
CREATE OR REPLACE TABLE my_table(
    a INT PRIMARY KEY,
    b INT
  );

exclude-from-coverage
statement ok
  INSERT INTO my_table (a, b) VALUES (1, NULL), (2, NULL), (3, NULL);

exclude-from-coverage
statement ok
DELETE FROM my_table WHERE a IS NULL;

exclude-from-coverage
statement ok
ALTER TABLE my_table ALTER COLUMN a SET NOT NULL;

query TT
SELECT * FROM my_table;
----
1	NULL
2	NULL
3	NULL

statement ok
CREATE OR REPLACE TABLE my_table(
    a INT PRIMARY KEY,
    c INT
  );

exclude-from-coverage
statement ok
INSERT INTO my_table (a, c) VALUES
  (1, 10),
  (2, 20),
  (3, 30);

query TT
SELECT * FROM my_table
----
1	10
2	20
3	30

statement ok
CREATE OR REPLACE TABLE t(a INT);

statement ok
CREATE OR ALTER TABLE t(a INT PRIMARY KEY);

query TTTTTTTTTTTT
DESC TABLE t
----
A	NUMBER(38,0)	COLUMN	N	NULL	Y	N	NULL	NULL	NULL	NULL	NULL

statement ok
CREATE OR REPLACE TABLE t(a INT);

# Test creating a table with duplicate column names (should error)

statement error
CREATE TABLE dup_col_test(x INT, x INT);
----
002025 (42S21): SQL compilation error:
duplicate column name 'X'

# Test creating a table with an invalid/unsupported data type

statement error
CREATE TABLE bad_type_test(col XYZ);
----
002040 (42601): SQL compilation error:
Unsupported data type 'XYZ'.