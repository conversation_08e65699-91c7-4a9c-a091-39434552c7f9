query TT
SELECT * FROM (VALUES (1, 'one'), (2, 'two'), (3, 'three'))
----
1	one
2	two
3	three

query TT
SELECT column1, column2 FROM (VALUES (1, 'one'), (2, 'two'), (3, 'three'))
----
1	one
2	two
3	three

query TT
SELECT v1.column2, v2.column2
  FROM (VALUES (1, 'one'), (2, 'two')) AS v1
        INNER JOIN (VALUES (1, 'One'), (3, 'three')) AS v2
  WHERE v2.column1 = v1.column1
----
one	One

query TT
SELECT c1, c2
  FROM (VALUES (1, 'one'), (2, 'two')) AS v1 (c1, c2)
----
1	one
2	two

# Test using a VALUES clause inside a CTE

query TT
WITH vals AS (
  SELECT *
  FROM VALUES (1,'x'),(2,'y') AS t(num, letter)
)
SELECT num, letter FROM vals ORDER BY num;
----
1	x
2	y