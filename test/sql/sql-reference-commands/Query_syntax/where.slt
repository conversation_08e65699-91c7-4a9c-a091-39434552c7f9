exclude-from-coverage
statement ok
CREATE OR R<PERSON>LACE TABLE invoices (
    invoice_id INTEGER,
    invoice_date DATE,
    amount DECIMAL,
    paid BOOLEAN
);

statement ok
SELECT * FROM invoices
  WHERE invoice_date < '2018-01-01';

statement ok
SELECT * FROM invoices
  WHERE invoice_date < '2018-01-01'
    AND paid = FALSE;

statement ok
SELECT * FROM invoices
    WHERE amount < (
                   SELECT AVG(amount)
                       FROM invoices
                   )
    ;

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE t1 (
    col1 INTEGER
);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE t2 (
    col1 INTEGER
);

statement ok
SELECT t1.col1, t2.col1
    FROM t1, t2
    WHERE t2.col1 = t1.col1
    ORDER BY 1, 2;

statement ok
SELECT t1.col1, t2.col1
    FROM t1 JOIN t2
        ON t2.col1 = t1.col1
    ORDER BY 1, 2;

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE departments (
    department_ID INTEGER,
    department_name VARCHAR,
    location VARCHAR
);

exclude-from-coverage
statement ok
INSERT INTO departments (department_id, department_name, location) VALUES
    (10, 'CUSTOMER SUPPORT', 'CHICAGO'),
    (40, 'RESEARCH', 'BOSTON'),
    (80, 'Department with no employees yet', 'CHICAGO'),
    (90, 'Department with no projects or employees yet', 'EREHWON')
    ;

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE projects (
    project_id integer,
    project_name varchar,
    department_id integer
);

exclude-from-coverage
statement ok
INSERT INTO projects (project_id, project_name, department_id) VALUES
    (4000, 'Detect fake product reviews', 40),
    (4001, 'Detect false insurance claims', 10),
    (9000, 'Project with no employees yet', 80),
    (9099, 'Project with no department or employees yet', NULL)
    ;

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE employees (
    employee_ID INTEGER,
    employee_name VARCHAR,
    department_id INTEGER,
    project_id INTEGER
);

exclude-from-coverage
statement ok
INSERT INTO employees (employee_id, employee_name, department_id, project_id)
  VALUES
    (1012, 'May Aidez', 10, NULL),
    (1040, 'Devi Nobel', 40, 4000),
    (1041, 'Alfred Mendeleev', 40, 4001)
    ;

statement ok
SELECT d.department_name, p.project_name, e.employee_name
    FROM  departments d, projects p, employees e
    WHERE
            p.department_id = d.department_id
        AND
            e.project_id = p.project_id
    ORDER BY d.department_id, p.project_id, e.employee_id;

statement ok
SELECT d.department_name, p.project_name, e.employee_name
    FROM  departments d, projects p, employees e
    WHERE
            p.department_id = d.department_id
        AND
            e.project_id = p.project_id(+)
    ORDER BY d.department_id, p.project_id, e.employee_id;

statement ok
SELECT d.department_name, p.project_name, e.employee_name
    FROM  departments d, projects p, employees e
    WHERE
            p.department_id(+) = d.department_id
        AND
            e.project_id(+) = p.project_id
    ORDER BY d.department_id, p.project_id, e.employee_id;

# Test using a SELECT alias in WHERE (should error)

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE wh_test(a INT);

exclude-from-coverage
statement ok
INSERT INTO wh_test VALUES (1),(2);

query T
SELECT a AS num FROM wh_test
WHERE num > 1;
----
2

# Test using an aggregate directly in WHERE (should error)

statement error
SELECT * FROM wh_test
WHERE SUM(a) > 0;
----
002035 (42601): SQL compilation error:
Invalid aggregate function in where clause [SUM(WH_TEST.A)]