exclude-from-coverage
statement ok
CREATE or replace <PERSON><PERSON>LE nurses (
  ID INTEGER,
  full_name VARCHAR,
  medical_license VARCHAR,   -- LVN, RN, etc.
  radio_license VARCHAR      -- Technician, General, Amateur Extra
  )
  ;

exclude-from-coverage
statement ok
INSERT INTO nurses
    (ID, full_name, medical_license, radio_license)
  VALUES
    (201, '<PERSON>', '<PERSON><PERSON>', 'Technician'),
    (202, '<PERSON>', '<PERSON><PERSON>', 'Technician'),
    (341, '<PERSON><PERSON>', '<PERSON><PERSON>', 'General'),
    (471, '<PERSON>', 'RN', 'Amateur Extra')
    ;

query TTT
SELECT COUNT(*), medical_license, radio_license
  FROM nurses
  GROUP BY GROUPING SETS (medical_license, radio_license)
----
3	LVN	NULL
1	RN	NULL
1	NULL	General
1	NULL	Amateur Extra
2	NULL	Technician

exclude-from-coverage
statement ok
INSERT INTO nurses
    (ID, full_name, medical_license, radio_license)
  VALUES
    (101, '<PERSON>', '<PERSON><PERSON>', NULL),
    (102, '<PERSON>', '<PERSON><PERSON>', NULL),
    (172, '<PERSON><PERSON><PERSON>', 'RN', NULL)
    ;

query TTT
SELECT COUNT(*), medical_license, radio_license
  FROM nurses
  GROUP BY medical_license, radio_license
----
1	LVN	General
2	LVN	Technician
1	RN	Amateur Extra
2	LVN	NULL
1	RN	NULL

# Test GROUPING() function with GROUPING SETS (including rollup totals)

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE gs_test(c1 INT, c2 INT, val INT);

exclude-from-coverage
statement ok
INSERT INTO gs_test VALUES (1,10,100),(1,20,200),(2,10,300);

query IIIII
SELECT c1, c2, GROUPING(c1) AS g1, GROUPING(c2) AS g2, SUM(val)
FROM gs_test
GROUP BY GROUPING SETS ((c1, c2), (c1), ())
ORDER BY c1 NULLS LAST, c2 NULLS LAST;
----
1	10	0	0	100
1	20	0	0	200
1	NULL	0	1	300
2	10	0	0	300
2	NULL	0	1	300
NULL	NULL	1	1	600