exclude-from-coverage
statement ok
create or replace table test_boolean_agg(
    id integer,
    c1 boolean, 
    c2 boolean,
    c3 boolean,
    c4 boolean
    );

exclude-from-coverage
statement ok
insert into test_boolean_agg (id, c1, c2, c3, c4) values 
    (1, true, true,  true,  false),
    (2, true, false, false, false),
    (3, true, true,  false, false),
    (4, true, false, false, false);

query TTTTT
select * from test_boolean_agg
----
1	TRUE	TRUE	TRUE	FALSE
2	TRUE	FALSE	FALSE	FALSE
3	TRUE	TRUE	FALSE	FALSE
4	TRUE	FALSE	FALSE	FALSE

query TTTT
select boolxor_agg(c1), boolxor_agg(c2), boolxor_agg(c3), boolxor_agg(c4)
    from test_boolean_agg
----
FALSE	FALSE	TRUE	FALSE

exclude-from-coverage
statement ok
insert into test_boolean_agg (id, c1, c2, c3, c4) values
    (-4, false, false, false, true),
    (-3, false, true,  true,  true),
    (-2, false, false, true,  true),
    (-1, false, true,  true,  true);

query TTTTT
select * 
    from test_boolean_agg
    order by id
----
-4	FALSE	FALSE	FALSE	TRUE
-3	FALSE	TRUE	TRUE	TRUE
-2	FALSE	FALSE	TRUE	TRUE
-1	FALSE	TRUE	TRUE	TRUE
1	TRUE	TRUE	TRUE	FALSE
2	TRUE	FALSE	FALSE	FALSE
3	TRUE	TRUE	FALSE	FALSE
4	TRUE	FALSE	FALSE	FALSE

query TTTTT
select 
      id, 
      boolxor_agg(c1) OVER (PARTITION BY (id > 0)),
      boolxor_agg(c2) OVER (PARTITION BY (id > 0)),
      boolxor_agg(c3) OVER (PARTITION BY (id > 0)),
      boolxor_agg(c4) OVER (PARTITION BY (id > 0))
    from test_boolean_agg
    order by id
----
-4	FALSE	FALSE	FALSE	FALSE
-3	FALSE	FALSE	FALSE	FALSE
-2	FALSE	FALSE	FALSE	FALSE
-1	FALSE	FALSE	FALSE	FALSE
1	FALSE	FALSE	TRUE	FALSE
2	FALSE	FALSE	TRUE	FALSE
3	FALSE	FALSE	TRUE	FALSE
4	FALSE	FALSE	TRUE	FALSE

