exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE padding_example (v VARCHAR, b BINARY);

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    'Hi',
    HEX_ENCODE('Hi');

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    '-123.00',
    HEX_ENCODE('-123.00');

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    'Twelve Dollars',
    TO_BINARY(HEX_ENCODE('Twelve Dollars'), 'HEX');

query TTT
SELECT v,
       LPAD(v, 10, ' ') AS pad_with_blank,
       LPAD(v, 10, '$') AS pad_with_dollar_sign
  FROM padding_example
  ORDER BY v
----
-123.00	   -123.00	$$$-123.00
Hi	        Hi	$$$$$$$$Hi
Twelve Dollars	Twelve Dol	Twelve Dol

query TTT
SELECT b,
       LPAD(b, 10, TO_BINARY(HEX_ENCODE(' '))) AS pad_with_blank,
       LPAD(b, 10, TO_BINARY(HEX_ENCODE('$'))) AS pad_with_dollar_sign
  FROM padding_example
  ORDER BY b
----
x'2d3132332e3030'	x'2020202d3132332e3030'	x'2424242d3132332e3030'
x'4869'	x'20202020202020204869'	x'24242424242424244869'
x'5477656c766520446f6c6c617273'	x'5477656c766520446f6c'	x'5477656c766520446f6c'

query T
SELECT LPAD('123.50', 19, '*_')
----
*_*_*_*_*_*_*123.50

