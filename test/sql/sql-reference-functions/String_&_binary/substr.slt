query T
SELECT SUBSTR('testing 1 2 3', 9, 3)
----
1 2

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE test_substr (
    base_value VARCHAR,
    start_value INT,
    length_value INT)
  AS SELECT
    column1,
    column2,
    column3
  FROM
    VALUES
      ('mystring', -1, 3),
      ('mystring', -3, 3),
      ('mystring', -3, 7),
      ('mystring', -5, 3),
      ('mystring', -7, 3),
      ('mystring', 0, 3),
      ('mystring', 0, 7),
      ('mystring', 1, 3),
      ('mystring', 1, 7),
      ('mystring', 3, 3),
      ('mystring', 3, 7),
      ('mystring', 5, 3),
      ('mystring', 5, 7),
      ('mystring', 7, 3),
      ('mystring', NULL, 3),
      ('mystring', 3, NULL);

query TTTT
SELECT base_value,
       start_value,
       length_value,
       SUBSTR(base_value, start_value, length_value) AS substring
  FROM test_substr
----
mystring	-1	3	g
mystring	-3	3	ing
mystring	-3	7	ing
mystring	-5	3	tri
mystring	-7	3	yst
mystring	0	3	mys
mystring	0	7	mystrin
mystring	1	3	mys
mystring	1	7	mystrin
mystring	3	3	str
mystring	3	7	string
mystring	5	3	rin
mystring	5	7	ring
mystring	7	3	ng
mystring	NULL	3	NULL
mystring	3	NULL	NULL

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE customer_contact_example (
    cust_id INT,
    cust_email VARCHAR,
    cust_phone VARCHAR,
    activation_date VARCHAR)
  AS SELECT
    column1,
    column2,
    column3,
    column4
  FROM
    VALUES
      (1, '<EMAIL>', '************', '20210320'),
      (2, '<EMAIL>', '************', '20240509'),
      (3, '<EMAIL>', '************', '20191017');

query TTT
SELECT cust_id,
       cust_email,
       SUBSTR(cust_email, POSITION('@' IN cust_email) + 1) AS domain
  FROM customer_contact_example
----
1	<EMAIL>	example.com
2	<EMAIL>	example.org
3	<EMAIL>	example.net

query TTT
SELECT cust_id,
       cust_phone,
       SUBSTR(cust_phone, 1, 3) AS area_code
  FROM customer_contact_example
----
1	************	800
2	************	800
3	************	800

query TTT
SELECT cust_id,
       cust_phone,
       SUBSTR(cust_phone, 5) AS phone_without_area_code
  FROM customer_contact_example
----
1	************	555-0100
2	************	555-0101
3	************	555-0102

query TTTTT
SELECT cust_id,
       activation_date,
       SUBSTR(activation_date, 1, 4) AS year,
       SUBSTR(activation_date, 5, 2) AS month,
       SUBSTR(activation_date, 7, 2) AS day
  FROM customer_contact_example
----
1	20210320	2021	03	20
2	20240509	2024	05	09
3	20191017	2019	10	17

