exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE collation1 (v VARCHAR COLLATE 'es');

exclude-from-coverage
statement ok
INSERT INTO collation1 (v) VALUES ('ñ');

query TTTT
SELECT v,
       COLLATION(v),
       COLLATE(v, 'es-ci'),
       COLLATION(COLLATE(v, 'es-ci'))
  FROM collation1
----
ñ	es	ñ	es-ci

query TTTTT
SELECT v,
       v = 'ñ' AS "COMPARISON TO LOWER CASE",
       v = 'Ñ' AS "COMPARISON TO UPPER CASE",
       COLLATE(v, 'es-ci'),
       COLLATE(v, 'es-ci') = 'Ñ'
  FROM collation1
----
ñ	TRUE	FALSE	ñ	TRUE

