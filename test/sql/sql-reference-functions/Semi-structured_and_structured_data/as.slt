exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE multiple_types_example (
  array1 VARIANT,
  array2 VARIANT,
  boolean1 VARIANT,
  char1 VARIANT,
  varchar1 VARIANT,
  decimal1 VARIANT,
  double1 VARIANT,
  integer1 VARIANT,
  object1 VARIANT);

exclude-from-coverage
statement ok
INSERT INTO multiple_types_example
  (array1, array2, boolean1, char1, varchar1,
   decimal1, double1, integer1, object1)
  SELECT
    TO_VARIANT(TO_ARRAY('Example')),
    TO_VARIANT(ARRAY_CONSTRUCT('Array-like', 'example')),
    TO_VARIANT(TRUE),
    TO_VARIANT('X'),
    TO_VARIANT('Y'),
    TO_VARIANT(1.23::DECIMAL(6, 3)),
    TO_VARIANT(3.21::DOUBLE),
    TO_VARIANT(15),
    TO_VARIANT(TO_OBJECT(PARSE_JSON('{"Tree": "Pine"}')));

query TTTTTTTTT
SELECT AS_ARRAY(array1) AS array1,
       AS_ARRAY(array2) AS array2,
       AS_BOOLEAN(boolean1) AS boolean,
       AS_CHAR(char1) AS char,
       AS_VARCHAR(varchar1) AS varchar,
       AS_DECIMAL(decimal1, 6, 3) AS decimal,
       AS_DOUBLE(double1) AS double,
       AS_INTEGER(integer1) AS integer,
       AS_OBJECT(object1) AS object
  FROM multiple_types_example
----
'["Example"]'	'["Array-like","example"]'	TRUE	X	Y	1.230	3.21	15	'{"Tree":"Pine"}'

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE vartab (n NUMBER(2), v VARIANT);

exclude-from-coverage
statement ok
INSERT INTO vartab
  SELECT column1 AS n, PARSE_JSON(column2) AS v
    FROM VALUES (1, 'null'), 
                (2, null), 
                (3, 'true'),
                (4, '-17'), 
                (5, '123.12'), 
                (6, '1.912e2'),
                (7, '"Om ara pa ca na dhih"  '), 
                (8, '[-1, 12, 289, 2188, false,]'), 
                (9, '{ "x" : "abc", "y" : false, "z": 10} ') 
       AS vals;

query TTT
SELECT n, AS_REAL(v), TYPEOF(v)
  FROM vartab
  ORDER BY n
----
1	NULL	NULL_VALUE
2	NULL	NULL
3	NULL	BOOLEAN
4	-17.0	INTEGER
5	123.12	DECIMAL
6	191.2	DOUBLE
7	NULL	VARCHAR
8	NULL	ARRAY
9	NULL	OBJECT

query T
SELECT AVG(AS_REAL(v)) FROM vartab
----
99.10666666666667

