exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE jdemo1 (v VARIANT);

exclude-from-coverage
statement ok
INSERT INTO jdemo1 SELECT PARSE_JSON('{"food":"bard"}');

query TTT
SELECT v, v:food, TO_JSON(v) FROM jdemo1
----
'{"food":"bard"}'	"bard"	'{"food":"bard"}'

query TTTT
SELECT TO_JSON(NULL), TO_JSON('null'::VARIANT),
       PARSE_JSON(NULL), PARSE_JSON('null')
----
NULL	"null"	NULL	null

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE jdemo2 (
  varchar1 VARCHAR, 
  variant1 VARIANT);

exclude-from-coverage
statement ok
INSERT INTO jdemo2 (varchar1) VALUES ('{"PI":3.14}');

exclude-from-coverage
statement ok
UPDATE jdemo2 SET variant1 = PARSE_JSON(varchar1);

query TTTTTT
SELECT varchar1, 
       PARSE_JSON(varchar1), 
       variant1, 
       <PERSON>_<PERSON>SO<PERSON>(variant1),
       PARSE_JSON(varchar1) = variant1, 
       TO_JSON(variant1) = varchar1
  FROM jdemo2
----
'{"PI":3.14}'	'{"PI":3.14}'	'{"PI":3.14}'	'{"PI":3.14}'	TRUE	TRUE

query TTT
SELECT TO_JSON(PARSE_JSON('{"b":1,"a":2}')),
       TO_JSON(PARSE_JSON('{"b":1,"a":2}')) = '{"b":1,"a":2}',
       TO_JSON(PARSE_JSON('{"b":1,"a":2}')) = '{"a":2,"b":1}'
----
'{"a":2,"b":1}'	FALSE	TRUE

