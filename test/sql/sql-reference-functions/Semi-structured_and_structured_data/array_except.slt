query T
SELECT ARRAY_EXCEPT(['A', 'B'], ['B', 'C'])
----
'["A"]'

query T
SELECT ARRAY_EXCEPT(['A', 'B', 'C'], ['B', 'C'])
----
'["A"]'

query T
SELECT ARRAY_EXCEPT(['A', 'B', 'B', 'B', 'C'], ['B'])
----
'["A","B","B","C"]'

query T
SELECT ARRAY_EXCEPT(['A', 'B'], ['A', 'B'])
----
'[]'

query T
SELECT ARRAY_EXCEPT(['A', NULL, NULL], ['B', NULL])
----
'["A",null]'

query T
SELECT ARRAY_EXCEPT(['A', NULL, NULL], [NULL, 'B', NULL])
----
'["A"]'

query T
SELECT ARRAY_EXCEPT([PARSE_JSON('{"a": 1, "b": 2}'), 1], [PARSE_JSON('{"a": 1, "b": 2}'), 3])
----
'[1]'

query T
SELECT ARRAY_EXCEPT(['A', 'B'], NULL)
----
NULL

