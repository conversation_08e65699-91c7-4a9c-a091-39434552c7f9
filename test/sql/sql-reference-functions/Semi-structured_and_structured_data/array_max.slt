query T
SELECT ARRAY_MAX([20, 0, NULL, 10, NULL])
----
20

query T
SELECT ARRAY_MAX([NULL, PARSE_JSON('null'), NULL])
----
null

query T
SELECT ARRAY_MAX([])
----
NULL

query T
SELECT ARRAY_MAX([NULL, NULL, NULL])
----
NULL

exclude-from-coverage
statement ok
ALTER SESSION SET DATE_OUTPUT_FORMAT = 'YYYY-MM-DD';

query T
SELECT ARRAY_MAX([date1::TIMESTAMP, timestamp1]) AS array_max
  FROM (
      VALUES ('1999-01-01'::DATE, '2023-12-09 22:09:26.000000000'::TIMESTAMP),
             ('2023-12-09'::DATE, '1999-01-01 22:09:26.000000000'::TIMESTAMP)
          AS t(date1, timestamp1)
      )
----
"2023-12-09 22:09:26.000"
"2023-12-09 00:00:00.000"

