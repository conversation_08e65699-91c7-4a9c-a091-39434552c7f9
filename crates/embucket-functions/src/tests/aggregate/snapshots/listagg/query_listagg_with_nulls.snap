---
source: crates/embucket-functions/src/tests/aggregate/listagg.rs
description: "\"SELECT LISTAGG(val, ', ') FROM (VALUES ('apple'), (NULL), ('banana'), (NULL), ('cherry')) AS t(val)\""
---
Ok(
    [
        "+---------------------------+",
        "| listagg(t.val,Utf8(\", \")) |",
        "+---------------------------+",
        "| apple, banana, cherry     |",
        "+---------------------------+",
    ],
)
