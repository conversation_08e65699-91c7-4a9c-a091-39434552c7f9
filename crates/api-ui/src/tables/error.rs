use crate::error::IntoStatusCode;
use axum::extract::multipart;
use core_executor::error::ExecutionError;
use core_metastore::error::MetastoreError;
use error_stack_trace;
use http::StatusCode;
use snafu::Location;
use snafu::prelude::*;

#[derive(Snafu)]
#[snafu(visibility(pub(crate)))]
#[error_stack_trace::debug]
pub enum Error {
    #[snafu(display("Upload file error: {source}"))]
    UploadFile {
        source: TableError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Get table statistics error: {source}"))]
    GetTableStatistics {
        source: TableError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Get table columns error: {source}"))]
    GetTableColumns {
        source: TableError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Get table rows error: {source}"))]
    GetTablePreviewData {
        source: TableError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Get tables error: {source}"))]
    GetTables {
        source: TableError,
        #[snafu(implicit)]
        location: Location,
    },
}

// kind of reusable table error
#[derive(Snafu)]
#[snafu(visibility(pub(crate)))]
#[error_stack_trace::debug]
pub enum TableError {
    #[snafu(display("Malformed multipart form data: {error}"))]
    MalformedMultipart {
        #[snafu(source)]
        error: multipart::MultipartError,
        #[snafu(implicit)]
        location: Location,
    },

    #[snafu(display("Malformed multipart file data: {error}"))]
    MalformedMultipartFileData {
        #[snafu(source)]
        error: multipart::MultipartError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Malformed file upload request"))]
    MalformedFileUploadRequest {
        #[snafu(implicit)]
        location: Location,
    },

    #[snafu(display("File field missing in form data"))]
    FileField {
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Execution error: {source}"))]
    Execution {
        source: core_executor::error::ExecutionError,
        #[snafu(implicit)]
        location: Location,
    },
    #[snafu(display("Metastore error: {source}"))]
    Metastore {
        source: MetastoreError,
        #[snafu(implicit)]
        location: Location,
    },
}

// Select which status code to return.
impl IntoStatusCode for Error {
    fn status_code(&self) -> StatusCode {
        match self {
            Self::UploadFile { source, .. }
            | Self::GetTableStatistics { source, .. }
            | Self::GetTableColumns { source, .. }
            | Self::GetTablePreviewData { source, .. }
            | Self::GetTables { source, .. } => match &source {
                TableError::Metastore { source, .. } => match &source {
                    MetastoreError::ObjectAlreadyExists { .. } => StatusCode::CONFLICT,
                    MetastoreError::DatabaseNotFound { .. }
                    | MetastoreError::SchemaNotFound { .. }
                    | MetastoreError::TableNotFound { .. }
                    | MetastoreError::Validation { .. } => StatusCode::BAD_REQUEST,
                    _ => StatusCode::INTERNAL_SERVER_ERROR,
                },
                TableError::Execution { source, .. } => match &source {
                    ExecutionError::TableNotFound { .. } => StatusCode::NOT_FOUND,
                    ExecutionError::DataFusion { .. } => StatusCode::UNPROCESSABLE_ENTITY,
                    ExecutionError::Arrow { .. } => StatusCode::BAD_REQUEST,
                    _ => StatusCode::INTERNAL_SERVER_ERROR,
                },
                TableError::FileField { .. }
                | TableError::MalformedFileUploadRequest { .. }
                | TableError::MalformedMultipart { .. }
                | TableError::MalformedMultipartFileData { .. } => StatusCode::BAD_REQUEST,
            },
        }
    }
}
