---
source: crates/core-executor/src/tests/query.rs
description: "\"SHOW SCHEMAS IN embucket STARTS WITH 'new_schema'\""
info: "PreQueries: CREATE SCHEMA embucket.new_schema"
snapshot_kind: text
---
Ok(
    [
        "+------------+------------+------+---------------+-------------+",
        "| created_on | name       | kind | database_name | schema_name |",
        "+------------+------------+------+---------------+-------------+",
        "|            | new_schema |      | embucket      |             |",
        "+------------+------------+------+---------------+-------------+",
    ],
)
