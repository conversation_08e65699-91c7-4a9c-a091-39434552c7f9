---
source: crates/core-executor/src/tests/query.rs
description: "\"SHOW VIEWS IN embucket.information_schema\""
snapshot_kind: text
---
Ok(
    [
        "+------------+-----------------+------+---------------+--------------------+",
        "| created_on | name            | kind | database_name | schema_name        |",
        "+------------+-----------------+------+---------------+--------------------+",
        "|            | columns         | VIEW | embucket      | information_schema |",
        "|            | databases       | VIEW | embucket      | information_schema |",
        "|            | df_settings     | VIEW | embucket      | information_schema |",
        "|            | navigation_tree | VIEW | embucket      | information_schema |",
        "|            | parameters      | VIEW | embucket      | information_schema |",
        "|            | routines        | VIEW | embucket      | information_schema |",
        "|            | schemata        | VIEW | embucket      | information_schema |",
        "|            | tables          | VIEW | embucket      | information_schema |",
        "|            | views           | VIEW | embucket      | information_schema |",
        "+------------+-----------------+------+---------------+--------------------+",
    ],
)
