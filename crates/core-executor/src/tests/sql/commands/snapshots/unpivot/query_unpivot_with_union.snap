---
source: crates/embucket-functions/src/tests/sql/commands/unpivot.rs
description: "\"SELECT *\n  FROM monthly_sales\n    UNPIVOT (sales FOR month IN (jan, feb))\n  UNION ALL\nSELECT *\n  FROM monthly_sales\n    UNPIVOT (sales FOR month IN (mar, apr))\n  ORDER BY empid, month;\""
info: "Setup queries: CREATE OR REPLACE TABLE monthly_sales(\n  empid INT,\n  dept TEXT,\n  jan INT,\n  feb INT,\n  mar INT,\n  apr INT); INSERT INTO monthly_sales VALUES\n  (1, 'electronics', 100, 200, 300, 100),\n  (2, 'clothes', 100, 300, 150, 200),\n  (3, 'cars', 200, 400, 100, 50),\n  (4, 'appliances', 100, NULL, 100, 50);"
---
Ok(
    [
        "+-------+-------------+-----+-----+-------+-------+",
        "| empid | dept        | mar | apr | month | sales |",
        "+-------+-------------+-----+-----+-------+-------+",
        "| 1     | electronics | 100 | 200 | apr   | 100   |",
        "| 1     | electronics | 300 | 100 | feb   | 200   |",
        "| 1     | electronics | 300 | 100 | jan   | 100   |",
        "| 1     | electronics | 100 | 200 | mar   | 300   |",
        "| 2     | clothes     | 100 | 300 | apr   | 200   |",
        "| 2     | clothes     | 150 | 200 | feb   | 300   |",
        "| 2     | clothes     | 150 | 200 | jan   | 100   |",
        "| 2     | clothes     | 100 | 300 | mar   | 150   |",
        "| 3     | cars        | 200 | 400 | apr   | 50    |",
        "| 3     | cars        | 100 | 50  | feb   | 400   |",
        "| 3     | cars        | 100 | 50  | jan   | 200   |",
        "| 3     | cars        | 200 | 400 | mar   | 100   |",
        "| 4     | appliances  | 100 |     | apr   | 50    |",
        "| 4     | appliances  | 100 | 50  | jan   | 100   |",
        "| 4     | appliances  | 100 |     | mar   | 100   |",
        "+-------+-------------+-----+-----+-------+-------+",
    ],
)
